import Taro from '@tarojs/taro';
import { TOKEN_NAME } from './constant';
import {
  refreshToken as apiRefreshToken,
  isTokenExpired,
  isRefreshTokenExpired,
} from '@/api/apiConfig';
import dayjs from 'dayjs';

/**
 * @function toUrl
 * @description 页面跳转
 * */
export const toUrl = url => {
  Taro.navigateTo({ url });
};

/**
 * @function goBack
 * @description 页面返回
 * */
export const goBack = num => {
  const pageLen = Taro.getCurrentPages().length;
  if (pageLen > num) {
    Taro.navigateBack({ delta: num });
  } else {
    Taro.switchTab({ url: '/pages/home/<USER>' });
  }
};
/**
 * @function loading
 * @description 显示/隐藏loading
 * */
export const loading = {
  show: (title = '加载中...', mask = false) => {
    Taro.showLoading({ title, mask });
  },
  hide: () => {
    Taro.hideLoading();
  },
};

/**
 * @function checkPhone
 * @description 正则验证手机号
 * */
export const checkPhone = (phone: string) => /^1(3|4|5|6|7|8|9)\d{9}$/.test(phone);

/**
 * @function toast
 * */
export const toast = {
  show: ({ title = '', icon = 'none', duration = 3000, mask = true }) => {
    Taro.showToast({
      title,
      // @ts-ignore
      icon,
      duration,
      mask,
    });
  },
  info: (title, duration = 3000) => {
    Taro.showToast({
      title,
      icon: 'none',
      duration,
    });
  },
  hide: () => {
    Taro.hideToast();
  },
};
/**
 * @function _localStorage
 * @description localStorage封装 本地存储
 * */
export const _localStorage = {
  getItem: key => {
    try {
      var value = Taro.getStorageSync(key);
      if (value) {
        return value;
      }
    } catch (err) {
      return err;
    }
  },
  setItem: (key, data) => {
    Taro.setStorageSync(key, data);
  },
  removeItem: key => {
    try {
      Taro.removeStorageSync(key);
    } catch (e) {
      // Do something when catch error
    }
  },
  clear: () => {
    try {
      Taro.clearStorageSync();
    } catch (e) {
      // Do something when catch error
    }
  },
};
/**
 *
 * @function getWeekDay
 * @description 获取星期几
 * @param {string} dateStr 日期字符串，格式为'YYYY-MM-DD'
 *
 * */
export const getWeekDay = (dateStr: string) => {
  const [month, day] = dateStr.split('-').map(Number);
  const date = new Date(new Date().getFullYear(), month - 1, day);
  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  return weekDays[date.getDay()];
};

/**
 * @function isUserLoggedIn
 * @description 检查用户是否已登录
 */
export const isUserLoggedIn = () => {
  const token = _localStorage.getItem(TOKEN_NAME);
  const refreshToken = _localStorage.getItem('refreshToken');
  return !!(token && refreshToken);
};

/**
 * @function clearUserSession
 * @description 清空用户会话信息
 */
export const clearUserSession = () => {
  // _localStorage.removeItem(TOKEN_NAME);
  // _localStorage.removeItem('refreshToken');
  // _localStorage.removeItem('uid');
  // _localStorage.removeItem('accessTokenTimeOut');
  // _localStorage.removeItem('refreshTokenTimeOut');
  _localStorage.clear();
};

/**
 * @function getUserInfo
 * @description 获取用户基本信息
 */
export const getUserInfo = () => {
  return {
    uid: _localStorage.getItem('uid'),
    accessToken: _localStorage.getItem(TOKEN_NAME),
    refreshToken: _localStorage.getItem('refreshToken'),
    accessTokenTimeOut: _localStorage.getItem('accessTokenTimeOut'),
    refreshTokenTimeOut: _localStorage.getItem('refreshTokenTimeOut'),
  };
};
/**
 * @function getUrlParams
 * @description 从URL获取查询参数
 * @param url 可选，默认使用当前页面URL
 * @returns 返回参数对象
 */
export const getUrlParams = (url?: string): Record<string, string> => {
  // 如果没有传入URL，则获取当前页面的URL
  const targetUrl = url || getCurrentPageUrl();

  // 检查URL是否包含查询参数
  if (!targetUrl || !targetUrl.includes('?')) {
    return {};
  }

  // 提取查询字符串部分
  const queryString = targetUrl.split('?')[1];
  if (!queryString) {
    return {};
  }

  // 解析查询参数
  const params: Record<string, string> = {};
  const pairs = queryString.split('&');

  pairs.forEach(pair => {
    const [key, value] = pair.split('=');
    if (key) {
      // 解码URL编码的参数
      params[decodeURIComponent(key)] = value ? decodeURIComponent(value) : '';
    }
  });

  return params;
};

/**
 * @function getUrlParam
 * @description 从URL获取指定的查询参数
 * @param key 参数名
 * @param url 可选，默认使用当前页面URL
 * @returns 返回参数值，不存在则返回null
 */
export const getUrlParam = (key: string, url?: string): string | null => {
  const params = getUrlParams(url);
  return params[key] || null;
};

/**
 * @function getCurrentPageUrl
 * @description 获取当前页面的完整URL（包括查询参数）
 * @returns 返回当前页面URL
 */
export const getCurrentPageUrl = (): string => {
  const pages = Taro.getCurrentPages();
  if (pages.length === 0) {
    return '';
  }

  const currentPage = pages[pages.length - 1];
  const route = currentPage.route || '';

  // 获取页面参数
  const options = (currentPage as any).options || {};

  // 构建完整URL
  let fullUrl = `/${route}`;

  // 添加查询参数
  const queryParams = Object.keys(options)
    .filter(key => options[key] !== undefined && options[key] !== null)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options[key])}`)
    .join('&');

  if (queryParams) {
    fullUrl += `?${queryParams}`;
  }

  return fullUrl;
};
/**
 * @function checkTokenStatus
 * @description 检查token状态
 */
export const checkTokenStatus = () => {
  const token = _localStorage.getItem(TOKEN_NAME);
  const refreshTokenValue = _localStorage.getItem('refreshToken');

  const hasToken = !!(token && refreshTokenValue);
  const tokenExpired = isTokenExpired();
  const refreshExpired = isRefreshTokenExpired();

  return {
    hasToken,
    isExpired: tokenExpired,
    needRefresh: hasToken && tokenExpired && !refreshExpired,
    refreshExpired,
  };
};

/**
 * @function refreshToken
 * @description 刷新token（重新导出）
 */
export const refreshToken = apiRefreshToken;

/**
 * @function formatDateForIOS
 * @description 将日期字符串转换为iOS兼容格式
 * @param {string} dateStr 日期字符串
 * @returns {string} iOS兼容的日期字符串
 */
export const formatDateForIOS = (dateStr: string): string => {
  if (!dateStr) return '';

  // 使用dayjs解析日期，然后转换为iOS兼容的ISO 8601格式
  try {
    const parsed = dayjs(dateStr);
    if (!parsed.isValid()) {
      console.error('Invalid date string:', dateStr);
      return '';
    }
    // 返回ISO 8601格式，iOS完全支持
    return parsed.toISOString();
  } catch (error) {
    console.error('Error formatting date for iOS:', error);
    return '';
  }
};

/**
 * @function createSafeDate
 * @description 使用dayjs创建一个安全的Date对象，兼容iOS
 * @param {string} dateStr 日期字符串
 * @returns {Date | null} Date对象或null
 */
export const createSafeDate = (dateStr: string): Date | null => {
  if (!dateStr) return null;

  try {
    const parsed = dayjs(dateStr);
    if (!parsed.isValid()) {
      console.error('Invalid date format:', dateStr);
      return null;
    }

    return parsed.toDate();
  } catch (error) {
    console.error('Error creating date:', error);
    return null;
  }
};

/**
 * @function parseDateSafely
 * @description 安全地解析日期字符串，返回dayjs对象
 * @param {string} dateStr 日期字符串
 * @returns {dayjs.Dayjs | null} dayjs对象或null
 */
export const parseDateSafely = (dateStr: string): dayjs.Dayjs | null => {
  if (!dateStr) return null;

  try {
    const parsed = dayjs(dateStr);
    if (!parsed.isValid()) {
      console.error('Invalid date format:', dateStr);
      return null;
    }

    return parsed;
  } catch (error) {
    console.error('Error parsing date:', error);
    return null;
  }
};

/**
 * @function getSystemInfo
 * @description 安全地获取系统信息，优先使用新的分离式API
 * @returns 系统信息对象
 */
export const getSystemInfo = () => {
  const defaultInfo = {
    statusBarHeight: 20,
    screenWidth: 375,
    screenHeight: 667,
    windowWidth: 375,
    windowHeight: 667,
    pixelRatio: 2,
    platform: 'unknown',
    system: 'unknown',
    version: 'unknown',
    brand: 'unknown',
    model: 'unknown',
    language: 'zh_CN',
    theme: 'light',
  };

  try {
    // 优先使用新的分离式API
    const windowInfo = Taro.getWindowInfo?.();
    const deviceInfo = Taro.getDeviceInfo?.();
    const appBaseInfo = Taro.getAppBaseInfo?.();
    const systemSetting = Taro.getSystemSetting?.();

    const combinedInfo = {
      ...defaultInfo,
      // 窗口信息
      ...(windowInfo && {
        statusBarHeight: windowInfo.statusBarHeight || defaultInfo.statusBarHeight,
        screenWidth: windowInfo.screenWidth || defaultInfo.screenWidth,
        screenHeight: windowInfo.screenHeight || defaultInfo.screenHeight,
        windowWidth: windowInfo.windowWidth || defaultInfo.windowWidth,
        windowHeight: windowInfo.windowHeight || defaultInfo.windowHeight,
        pixelRatio: windowInfo.pixelRatio || defaultInfo.pixelRatio,
      }),
      // 设备信息
      ...(deviceInfo && {
        platform: deviceInfo.platform || defaultInfo.platform,
        system: deviceInfo.system || defaultInfo.system,
        brand: deviceInfo.brand || defaultInfo.brand,
        model: deviceInfo.model || defaultInfo.model,
      }),
      // 应用基础信息
      ...(appBaseInfo && {
        version: appBaseInfo.version || defaultInfo.version,
        language: appBaseInfo.language || defaultInfo.language,
        theme: appBaseInfo.theme || defaultInfo.theme,
      }),
      // 系统设置
      ...(systemSetting &&
        {
          // 系统设置相关信息
        }),
    };

    return combinedInfo;
  } catch (error) {
    console.warn('新API获取系统信息失败，尝试使用旧API:', error);

    // 降级处理：使用旧的getSystemInfoSync
    try {
      if (Taro.getSystemInfoSync) {
        const sysInfo = Taro.getSystemInfoSync();
        return {
          ...defaultInfo,
          ...sysInfo,
        };
      }
    } catch (fallbackError) {
      console.warn('旧API也获取系统信息失败:', fallbackError);
    }

    return defaultInfo;
  }
};

/**
 * @function getStatusBarHeight
 * @description 获取状态栏高度
 * @returns {number} 状态栏高度
 */
export const getStatusBarHeight = (): number => {
  try {
    // 优先使用新API
    const windowInfo = Taro.getWindowInfo?.();
    if (windowInfo?.statusBarHeight) {
      return windowInfo.statusBarHeight;
    }

    // 降级使用旧API
    const systemInfo = getSystemInfo();
    return systemInfo.statusBarHeight || 20;
  } catch (error) {
    console.warn('获取状态栏高度失败:', error);
    return 20; // 默认值
  }
};
/**
 * @function formatDate
 * @description 格式化日期显示
 * @param {string} dateStr 日期字符串 如2025-01-01
 * @returns {string} 格式化后的日期字符串 如：08月24日 周四
 */
export const formatDate = (dateStr?: string) => {
  if (!dateStr) return '';
  const date = dayjs(dateStr);
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  return `${date.format('MM月DD日')} ${weekdays[date.day()]}`;
};
/**
 * @function showDateWeek
 * @description 显示星期
 * @param {string} dateStr 日期字符串 如2025-01-01
 * @returns {string} 格式化后的日期字符串 周四
 */
export const showDateWeek = (dateStr?: string) => {
  if (!dateStr) return '';
  const date = dayjs(dateStr);
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  return weekdays[date.day()];
};

/**
 * @function maskCertNo
 * @description 脱敏显示证件号
 * @param {string} certNo 证件号
 */
export const maskCertNo = (certNo?: string) => {
  if (!certNo) return '';
  if (certNo.length <= 8) return certNo;
  return certNo.substring(0, 4) + '****' + certNo.substring(certNo.length - 4);
};
/**
 * @function copyText
 * @description 复制到剪贴板
 * @param {string} data 要复制的内容
 */
export const copyText = (data: string) => {
  Taro.setClipboardData({
    data: data,
    success: () => {
      toast.info('复制成功');
    },
  });
};

/**
 * @function payTips
 * @description 支付前提示弹窗管理
 */
export const payTips = {
  /**
   * 显示支付提示弹窗
   * @param onConfirm 确认回调函数
   * @param onCancel 取消回调函数（可选）
   * @returns Promise<boolean> 用户是否确认
   */
  show: (onConfirm?: () => void, onCancel?: () => void): Promise<boolean> => {
    return new Promise((resolve) => {
      // 创建一个全局事件来触发弹窗显示
      const eventData = {
        show: true,
        onConfirm: () => {
          onConfirm?.();
          resolve(true);
          // 隐藏弹窗
          Taro.eventCenter.trigger('payTipsEvent', { show: false });
        },
        onCancel: () => {
          onCancel?.();
          resolve(false);
          // 隐藏弹窗
          Taro.eventCenter.trigger('payTipsEvent', { show: false });
        },
      };

      // 触发全局事件
      Taro.eventCenter.trigger('payTipsEvent', eventData);
    });
  },

  /**
   * 隐藏支付提示弹窗
   */
  hide: () => {
    Taro.eventCenter.trigger('payTipsEvent', { show: false });
  },
};
