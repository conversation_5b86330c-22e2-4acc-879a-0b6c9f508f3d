import { memo } from 'react';
import CustomActionSheet from '@/components/CustomActionSheet';
import './index.less';
import { Button, View } from '@tarojs/components';

interface PayTipsProps {
  show: boolean;
  onCancel?: () => void;
  onConfirm?: () => void;
}
const PayTips = ({ show, onCancel, onConfirm }: PayTipsProps) => {
  return (
    <>
      <CustomActionSheet
        title={`温馨提示`}
        visible={show}
        onCancel={() => onCancel?.()}
        onConfirm={() => onConfirm?.()}
      >
        <View className={'pay-tips'}>
          <View className={'tab-container'}>
            <View className={'tab-item'}>
              <View className={'tab-text'}>购票须知</View>
              <View className={'tab-line'}></View>
            </View>
            <View className={'tab-content'}></View>
          </View>
          <Button className={'confirm-btn'} onClick={() => onConfirm?.()}>
            我已阅读并同意
          </Button>
        </View>
      </CustomActionSheet>
    </>
  );
};
export default memo(PayTips);
