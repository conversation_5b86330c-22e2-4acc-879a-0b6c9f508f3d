import { memo, useEffect, useState } from 'react';
import { View } from '@tarojs/components';
import './index.less';
import { Location } from '@nutui/icons-react-taro';
import Taro from '@tarojs/taro';
import api from '@/api';
import { SUCCESS_CODE } from '@/utils/constant';

const Position = ({ getData }) => {
  const [cityObj, setCityObj] = useState<any>({});

  const getLocation = () => {
    Taro.getLocation({
      type: 'gcj02', // 使用国测局坐标系
      success: async res => {
        // console.log(res);
        const { code, data } = await api.v10LocationCityCreate({
          longitude: String(res.longitude),
          latitude: String(res.latitude),
        });
        if (code === SUCCESS_CODE) {
          setCityObj(data);
          getData?.(data);
        }
      },
      fail: function () {
        setCityObj({ name: '定位失败' });
      },
    });
  };
  useEffect(() => {
    getLocation();
  }, []);
  return (
    <>
      <View className='position-container'>
        <View className='position-text'>{cityObj?.cityName || '定位中...'}</View>
        <Location width={'10px'} height={'14px'} color='#999' />
      </View>
    </>
  );
};
export default memo(Position);
