import React from 'react';
import { View, Button } from '@tarojs/components';
import { payTips } from '@/utils';

// 使用示例组件
const PayTipsExample = () => {
  // 示例1: 基本用法 - 使用 Promise
  const handleBasicPay = async () => {
    const confirmed = await payTips.show();
    if (confirmed) {
      console.log('用户确认支付，开始执行支付逻辑');
      // 这里执行实际的支付逻辑
    } else {
      console.log('用户取消支付');
    }
  };

  // 示例2: 带回调函数的用法
  const handlePayWithCallback = () => {
    payTips.show(
      () => {
        // 确认回调
        console.log('用户确认支付');
        executePayment();
      },
      () => {
        // 取消回调
        console.log('用户取消支付');
      }
    );
  };

  // 示例3: 复杂的支付流程
  const handleComplexPay = async () => {
    try {
      // 显示支付提示
      const confirmed = await payTips.show();
      
      if (confirmed) {
        // 执行支付前的验证
        const isValid = await validatePaymentInfo();
        
        if (isValid) {
          // 执行支付
          await executePayment();
          console.log('支付成功');
        } else {
          console.log('支付信息验证失败');
        }
      }
    } catch (error) {
      console.error('支付过程中出错:', error);
    }
  };

  // 模拟支付逻辑
  const executePayment = async () => {
    console.log('正在执行支付...');
    // 模拟异步支付请求
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('支付完成');
        resolve(true);
      }, 2000);
    });
  };

  // 模拟支付信息验证
  const validatePaymentInfo = async () => {
    console.log('验证支付信息...');
    // 模拟验证逻辑
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, 1000);
    });
  };

  return (
    <View style={{ padding: '20px' }}>
      <View style={{ marginBottom: '20px' }}>
        <Button onClick={handleBasicPay}>
          基本用法 - Promise 方式
        </Button>
      </View>
      
      <View style={{ marginBottom: '20px' }}>
        <Button onClick={handlePayWithCallback}>
          回调函数方式
        </Button>
      </View>
      
      <View style={{ marginBottom: '20px' }}>
        <Button onClick={handleComplexPay}>
          复杂支付流程
        </Button>
      </View>
    </View>
  );
};

export default PayTipsExample;
