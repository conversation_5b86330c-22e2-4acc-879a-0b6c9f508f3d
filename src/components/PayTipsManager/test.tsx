import React from 'react';
import { View, Button } from '@tarojs/components';
import { payTips } from '@/utils';

const PayTipsTest = () => {
  const handleTest = () => {
    console.log('开始测试支付弹窗');
    
    payTips.show(
      () => {
        console.log('用户确认支付');
      },
      () => {
        console.log('用户取消支付');
      }
    );
  };

  const handleTestPromise = async () => {
    console.log('开始测试支付弹窗 Promise 方式');
    
    const result = await payTips.show();
    console.log('用户选择结果:', result);
  };

  return (
    <View style={{ padding: '20px' }}>
      <Button onClick={handleTest} style={{ marginBottom: '20px' }}>
        测试支付弹窗（回调方式）
      </Button>
      
      <Button onClick={handleTestPromise}>
        测试支付弹窗（Promise 方式）
      </Button>
    </View>
  );
};

export default PayTipsTest;
