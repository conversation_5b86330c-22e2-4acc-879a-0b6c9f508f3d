# PayTips 支付提示弹窗使用说明

## 概述
PayTips 是一个全局的支付前提示弹窗管理器，可以在任何页面方便地调用支付提示弹窗。

## 使用方法

### 1. 基本用法

```typescript
import { payTips } from '@/utils';

// 简单调用
const handlePay = async () => {
  const confirmed = await payTips.show();
  if (confirmed) {
    // 用户确认，执行支付逻辑
    console.log('用户确认支付');
  } else {
    // 用户取消
    console.log('用户取消支付');
  }
};
```

### 2. 带回调函数的用法

```typescript
import { payTips } from '@/utils';

const handlePay = () => {
  payTips.show(
    () => {
      // 确认回调
      console.log('用户确认支付');
      // 执行支付逻辑
      doPayment();
    },
    () => {
      // 取消回调（可选）
      console.log('用户取消支付');
    }
  );
};

const doPayment = () => {
  // 实际的支付逻辑
  console.log('执行支付...');
};
```

### 3. 使用 Promise 方式

```typescript
import { payTips } from '@/utils';

const handlePay = async () => {
  try {
    const confirmed = await payTips.show();
    
    if (confirmed) {
      // 执行支付逻辑
      await performPayment();
      console.log('支付成功');
    }
  } catch (error) {
    console.error('支付过程中出错:', error);
  }
};
```

### 4. 手动隐藏弹窗

```typescript
import { payTips } from '@/utils';

// 在某些情况下需要手动隐藏弹窗
const hidePayTips = () => {
  payTips.hide();
};
```

## API 说明

### payTips.show(onConfirm?, onCancel?)

显示支付提示弹窗

**参数:**
- `onConfirm?: () => void` - 用户确认时的回调函数（可选）
- `onCancel?: () => void` - 用户取消时的回调函数（可选）

**返回值:**
- `Promise<boolean>` - 返回一个 Promise，resolve 值为 true 表示用户确认，false 表示用户取消

### payTips.hide()

手动隐藏支付提示弹窗

## 注意事项

1. **全局唯一**: 同一时间只能显示一个支付提示弹窗
2. **自动管理**: 弹窗会在用户操作后自动隐藏，无需手动调用 hide()
3. **事件驱动**: 基于 Taro 的事件中心实现，确保在任何页面都能正常工作
4. **类型安全**: 完全支持 TypeScript，提供完整的类型提示

## 实现原理

1. 在 `src/utils/index.ts` 中定义了 `payTips` 工具函数
2. 在 `src/components/PayTipsManager` 中实现了全局弹窗管理器
3. 在 `src/app.tsx` 中注册了全局管理器组件
4. 通过 Taro 的事件中心进行组件间通信

这样的设计确保了：
- 调用简单方便
- 不需要在每个页面都引入组件
- 全局状态管理
- 完全解耦的实现
