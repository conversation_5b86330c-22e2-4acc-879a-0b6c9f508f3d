import { useState, useEffect } from 'react';
import Taro from '@tarojs/taro';
import { Button, View } from '@tarojs/components';
import CustomActionSheet from '@/components/CustomActionSheet';
import './index.less';
interface PayTipsEventData {
  show: boolean;
  onConfirm?: () => void;
  onCancel?: () => void;
}

const PayTipsManager = () => {
  const [visible, setVisible] = useState(false);
  const [confirmHandler, setConfirmHandler] = useState<(() => void) | null>(null);
  const [cancelHandler, setCancelHandler] = useState<(() => void) | null>(null);

  useEffect(() => {
    const handlePayTipsEvent = (eventData: PayTipsEventData) => {
      console.log(eventData);
      setVisible(eventData.show);

      if (eventData.show) {
        // 使用函数包装来避免闭包问题
        setConfirmHandler(() => eventData.onConfirm || null);
        setCancelHandler(() => eventData.onCancel || null);
      } else {
        setConfirmHandler(null);
        setCancelHandler(null);
      }
    };

    // 监听全局事件
    Taro.eventCenter.on('payTipsEvent', handlePayTipsEvent);

    // 清理事件监听
    return () => {
      Taro.eventCenter.off('payTipsEvent', handlePayTipsEvent);
    };
  }, []);

  const handleConfirm = () => {
    confirmHandler?.();
  };

  const handleCancel = () => {
    cancelHandler?.();
  };

  return (
    <CustomActionSheet
      title={`温馨提示`}
      visible={visible}
      onCancel={() => handleCancel?.()}
      onConfirm={() => handleConfirm?.()}
    >
      <View className={'pay-tips'}>
        <View className={'tab-container'}>
          <View className={'tab-item'}>
            <View className={'tab-text'}>购票须知</View>
            <View className={'tab-line'}></View>
          </View>
          <View className={'tab-content'}></View>
        </View>
        <Button className={'confirm-btn'} onClick={() => handleConfirm?.()}>
          我已阅读并同意
        </Button>
      </View>
    </CustomActionSheet>
  );
};

export default PayTipsManager;
