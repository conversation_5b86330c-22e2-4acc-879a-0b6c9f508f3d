import React, { useState, useEffect, useRef } from 'react';
import Taro from '@tarojs/taro';
import { Button, View } from '@tarojs/components';
import { ActionSheet } from '@nutui/nutui-react-taro';

interface PayTipsEventData {
  show: boolean;
  onConfirm?: () => void;
  onCancel?: () => void;
}

const PayTipsManagerDebug = () => {
  const [visible, setVisible] = useState(false);
  const confirmHandlerRef = useRef<(() => void) | null>(null);
  const cancelHandlerRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    const handlePayTipsEvent = (eventData: PayTipsEventData) => {
      console.log('PayTips事件接收到:', eventData);
      setVisible(eventData.show);

      if (eventData.show) {
        confirmHandlerRef.current = eventData.onConfirm || null;
        cancelHandlerRef.current = eventData.onCancel || null;
        console.log('设置回调函数:', {
          hasConfirm: !!eventData.onConfirm,
          hasCancel: !!eventData.onCancel
        });
      } else {
        confirmHandlerRef.current = null;
        cancelHandlerRef.current = null;
      }
    };

    // 监听全局事件
    Taro.eventCenter.on('payTipsEvent', handlePayTipsEvent);
    console.log('PayTipsManager: 事件监听器已注册');

    // 清理事件监听
    return () => {
      Taro.eventCenter.off('payTipsEvent', handlePayTipsEvent);
      console.log('PayTipsManager: 事件监听器已清理');
    };
  }, []);

  const handleConfirm = () => {
    console.log('确认按钮被点击, 回调函数:', confirmHandlerRef.current);
    if (confirmHandlerRef.current) {
      confirmHandlerRef.current();
    }
  };

  const handleCancel = () => {
    console.log('取消按钮被点击, 回调函数:', cancelHandlerRef.current);
    if (cancelHandlerRef.current) {
      cancelHandlerRef.current();
    }
  };

  console.log('PayTipsManager 渲染, visible:', visible);

  return (
    <ActionSheet
      visible={visible}
      onCancel={handleCancel}
    >
      <View style={{ padding: '16px' }}>
        <View style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          borderBottom: '1px solid #E6EAF0',
          paddingBottom: '16px',
          marginBottom: '16px'
        }}>
          <View style={{ fontSize: '20px', fontWeight: 'bold' }}>温馨提示</View>
          <Button 
            style={{ 
              padding: '0', 
              background: 'transparent',
              border: 'none'
            }} 
            onClick={handleCancel}
          >
            ✕
          </Button>
        </View>

        <View>
          <View style={{ marginBottom: '16px' }}>
            <View style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '8px' }}>
              购票须知
            </View>
            <View style={{ fontSize: '14px', color: '#666' }}>
              请仔细阅读购票须知，确认无误后点击确认按钮。
            </View>
          </View>
          
          <Button 
            style={{
              width: '100%',
              height: '48px',
              borderRadius: '12px',
              backgroundColor: '#007AFF',
              color: 'white',
              fontSize: '16px',
              fontWeight: 'bold',
              border: 'none'
            }}
            onClick={handleConfirm}
          >
            我已阅读并同意
          </Button>
        </View>
      </View>
    </ActionSheet>
  );
};

export default PayTipsManagerDebug;
