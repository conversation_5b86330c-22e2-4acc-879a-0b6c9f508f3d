import { memo, useState } from 'react';
import { ScrollView, Swiper, SwiperItem, View, Image, Text } from '@tarojs/components';
import CustomHeader from '@/components/CustomHeader';
import Position from '@/components/Position';
import './index.less';
import { airTour, jaunt, lowAltitudeTourism } from '@/utils/img';
import { payTips, toast, toUrl } from '@/utils';
import RecommendList from '@/components/RecommendList';
import api from '@/api';
import { SUCCESS_CODE } from '@/utils/constant';

const HOME_NAV_MENU = [
  { name: '低空交通', imgUrl: jaunt, path: '/pages/jauntAirLine/index' },
  { name: '低空游览', imgUrl: airTour, path: '/pages/product/index' },
  { name: '低空专机', imgUrl: lowAltitudeTourism, path: '/pages/lowAltitudeAirline/index' },
];
const Index = () => {
  const [pageData, setPageData] = useState<any>();

  const getData = async city => {
    const { code, data } = await api.v10IndexBannerHotCreate({
      currentCity: city?.cityName,
      currentCityId: city?.cityId,
    });
    if (code === SUCCESS_CODE) {
      setPageData(data);
      console.log(data);
    }
    const res = await payTips.show();
    console.log(res);
    console.log(1111);
  };

  return (
    <>
      <View className={'home-page'}>
        <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
          <CustomHeader
            customElement={
              <Position
                getData={city => {
                  getData(city);
                }}
              />
            }
            bgColor={'transparent'}
          />
          <View className={'swapper-box'}>
            <Swiper
              className='test-h'
              indicatorColor='#fff'
              indicatorActiveColor='#F3564B'
              circular
              indicatorDots
              autoplay
            >
              {pageData?.banners?.map((item, index) => (
                <SwiperItem key={index}>
                  <Image src={item.imageUrl} className={'swiper-bg'} />
                </SwiperItem>
              ))}
            </Swiper>
          </View>
          <View className={'home-nav-menu'}>
            {HOME_NAV_MENU?.map((item, index) => (
              <View
                className={'nav-menu-item'}
                key={index}
                style={{
                  background: `url(${item.imgUrl}) no-repeat center center/cover`,
                }}
                onClick={() => {
                  // if (!_localStorage.getItem(TOKEN_NAME)) {
                  //   toast.info('请先登录');
                  //   toUrl('/pages/login/index');
                  //   return;
                  // }
                  //todo 注释低空专机
                  if (item?.name === '低空专机') {
                    toast.info('暂未开放，敬请期待！');
                    return;
                  }
                  toUrl(item?.path);
                }}
              >
                <View className={'name'}>{item?.name}</View>
                <View className={'get-btn'}>立即预定</View>
              </View>
            ))}
          </View>
          <View className={'hot-recommend'}>
            <View className={'title'}>
              <Text>热门推荐</Text>
            </View>
            <RecommendList type={'home'} listData={pageData?.hotProducts} />
          </View>
        </ScrollView>
      </View>
    </>
  );
};
export default memo(Index);
